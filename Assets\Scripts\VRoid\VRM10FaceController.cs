using UnityEngine;
using UniVRM10;
using System.Linq;

namespace VRoidFaceCustomization
{
    /// <summary>
    /// VRM 1.0 面部表情控制器
    /// 
    /// <para>
    /// 专门负责VRM 1.0模型的面部表情控制和BlendShape参数管理。
    /// 支持标准VRM表情（happy、sad、angry等）和自定义BlendShape参数。
    /// 提供实时表情控制、参数插值和批量操作功能。
    /// </para>
    /// 
    /// <para><b>主要功能：</b></para>
    /// <list type="bullet">
    /// <item><description>🎭 标准VRM表情控制 - 支持VRM 1.0标准表情库</description></item>
    /// <item><description>🔧 自定义BlendShape - 精确控制单个面部参数</description></item>
    /// <item><description>📊 参数管理 - 批量设置、重置和随机化</description></item>
    /// <item><description>⚡ 实时控制 - 支持Inspector和代码实时调整</description></item>
    /// <item><description>🎨 参数插值 - 平滑的表情过渡动画</description></item>
    /// <item><description>🌐 WebGL兼容 - 支持网页端表情控制</description></item>
    /// </list>
    /// 
    /// <para><b>使用示例：</b></para>
    /// <code>
    /// var faceController = GetComponent&lt;VRM10FaceController&gt;();
    /// 
    /// // 设置标准表情
    /// faceController.SetExpression("happy", 1.0f);
    /// faceController.SetExpression("blink", 0.5f);
    /// 
    /// // 自定义BlendShape控制
    /// faceController.SetBlendShapeParameter("EyeBrow", 0.8f);
    /// 
    /// // 批量操作
    /// faceController.ResetFacialParameters();
    /// faceController.RandomizeFacialParameters();
    /// 
    /// // 获取当前参数
    /// var facialData = faceController.GetFacialParametersData();
    /// </code>
    /// 
    /// <para><b>支持的表情类型：</b></para>
    /// <list type="table">
    /// <item><term>happy</term><description>开心表情</description></item>
    /// <item><term>angry</term><description>愤怒表情</description></item>
    /// <item><term>sad</term><description>悲伤表情</description></item>
    /// <item><term>relaxed</term><description>放松表情</description></item>
    /// <item><term>surprised</term><description>惊讶表情</description></item>
    /// <item><term>blink</term><description>眨眼动作</description></item>
    /// <item><term>blinkL/blinkR</term><description>左右眼单独眨眼</description></item>
    /// </list>
    /// 
    /// <para><b>注意事项：</b></para>
    /// <list type="number">
    /// <item><description>需要有效的VRM 1.0模型和Vrm10Instance组件</description></item>
    /// <item><description>参数值范围为0.0-1.0，超出范围会被自动限制</description></item>
    /// <item><description>建议在模型完全加载后再进行表情控制</description></item>
    /// </list>
    /// 
    /// <para><b>版本：</b> 1.0.0</para>
    /// <para><b>依赖：</b> UniVRM10, VRM10FacialParameters</para>
    /// </summary>
    public class VRM10FaceController : MonoBehaviour
    {
        [Header("VRM 组件")]
        [SerializeField] private Vrm10Instance vrmInstance;

        [Header("面部参数")]
        [SerializeField] private VRM10FacialParameters facialParameters;

        [Header("设置选项")]
        [SerializeField] private bool autoInitializeOnStart = true;
        [SerializeField] private bool enableInspectorRealtime = true; // 用于Inspector显示
        [SerializeField] private bool debugMode = false;
        [SerializeField] private bool showDetailedLogs = false;

        public event System.Action<string, float> OnParameterChanged;
        public event System.Action OnParametersReset;
        public event System.Action OnParametersRandomized; // 预留的事件接口

        private bool isInitialized = false;

        void Start()
        {
            if (vrmInstance == null)
            {
                vrmInstance = GetComponentInParent<Vrm10Instance>();
            }

            if (autoInitializeOnStart)
            {
                InitializeFaceController();
            }
        }

        public void InitializeFaceController()
        {
            if (isInitialized)
            {
                if (showDetailedLogs) Debug.Log("[VRM10FaceController] 已经初始化，跳过");
                return;
            }

            // 自动检测VRM实例（支持Editor模式和运行时模式）
            if (vrmInstance == null)
            {
                // 首先尝试在当前GameObject上查找
                vrmInstance = GetComponent<Vrm10Instance>();

                // 如果没找到，尝试在父级GameObject上查找
                if (vrmInstance == null)
                {
                    vrmInstance = GetComponentInParent<Vrm10Instance>();
                }

                // 如果还没找到，尝试在子级GameObject上查找
                if (vrmInstance == null)
                {
                    vrmInstance = GetComponentInChildren<Vrm10Instance>();
                }

                // 记录自动检测结果
                if (vrmInstance != null)
                {
                    if (debugMode) Debug.Log($"[VRM10FaceController] 自动检测到VRM实例: {vrmInstance.name}");
                }
            }

            if (vrmInstance == null)
            {
                Debug.LogError("[VRM10FaceController] 无法找到VRM10Instance组件！请确保VRM模型已正确加载，或手动拖拽VRM对象到VRM Instance字段。");
                return;
            }

            if (debugMode) Debug.Log("[VRM10FaceController] 开始初始化面部控制器...");

            if (facialParameters == null)
            {
                facialParameters = new VRM10FacialParameters();
            }

            // 在编辑模式下，避免完整的Runtime初始化以防止材质警告
            if (!Application.isPlaying)
            {
                if (debugMode) Debug.LogWarning("[VRM10FaceController] 编辑模式下进行简化初始化，避免材质警告。完整功能需要在Play模式下使用。");
                InitializeEditorMode();
            }
            else
            {
                facialParameters.Initialize(vrmInstance);
            }

            isInitialized = true;

            if (debugMode) Debug.Log("[VRM10FaceController] VRM 1.0面部控制器初始化完成！");
        }

        /// <summary>
        /// 编辑模式下的简化初始化，避免触发材质警告
        /// </summary>
        private void InitializeEditorMode()
        {
            if (facialParameters == null) return;

            // 清空现有参数
            facialParameters.standardExpressions.Clear();
            facialParameters.customExpressions.Clear();

            // 只初始化基本的表情参数，不访问Runtime
            if (vrmInstance != null && vrmInstance.Vrm != null && vrmInstance.Vrm.Expression != null)
            {
                // 从VRM配置中获取表情信息，而不是从Runtime
                foreach (var clip in vrmInstance.Vrm.Expression.Clips)
                {
                    if (clip.Clip != null)
                    {
                        var expr = new FacialParameter
                        {
                            name = clip.Clip.name,
                            displayName = clip.Clip.name,
                            isExpression = true,
                        };
                        facialParameters.standardExpressions.Add(expr);
                    }
                }

                if (debugMode) Debug.Log($"[VRM10FaceController] 编辑模式初始化完成 - 表情: {facialParameters.standardExpressions.Count}");
            }
        }

        public bool IsInitialized()
        {
            return isInitialized;
        }

        public void SetExpression(string expressionName, float weight)
        {
            if (!isInitialized)
            {
                if (debugMode) Debug.LogWarning("[VRM10FaceController] 控制器未初始化，无法设置表情");
                return;
            }

            if (showDetailedLogs) Debug.Log($"[VRM10FaceController] 设置表情: {expressionName} = {weight}");

            // 在编辑模式下，只更新参数值，不访问Runtime
            if (!Application.isPlaying)
            {
                var param = facialParameters.GetParameter(expressionName);
                if (param != null)
                {
                    param.currentValue = weight;
                    OnParameterChanged?.Invoke(expressionName, weight);
                }
                else if (debugMode)
                {
                    Debug.LogWarning($"[VRM10FaceController] 编辑模式下未找到表情参数: {expressionName}");
                }
                return;
            }

            // Play模式下的完整功能
            var expressionProxy = vrmInstance.Runtime.Expression;
            ExpressionKey key;

            // 修正：通过 Enum.TryParse 区分标准预设表情和自定义表情
            if (System.Enum.TryParse<ExpressionPreset>(expressionName, true, out var preset))
            {
                key = new ExpressionKey(preset);
            }
            else
            {
                key = ExpressionKey.CreateCustom(expressionName);
            }

            expressionProxy.SetWeight(key, weight);

            var param = facialParameters.GetParameter(expressionName);
            if (param != null)
            {
                param.currentValue = weight;
                OnParameterChanged?.Invoke(expressionName, weight);
            }
        }

        /// <summary>
        /// 强制进行完整的Runtime初始化（主要用于从编辑模式切换到Play模式）
        /// </summary>
        public void ForceFullInitialization()
        {
            if (vrmInstance == null)
            {
                Debug.LogError("[VRM10FaceController] 无法进行完整初始化：VRM实例为空");
                return;
            }

            if (facialParameters == null)
            {
                facialParameters = new VRM10FacialParameters();
            }

            // 强制进行完整的Runtime初始化
            facialParameters.Initialize(vrmInstance);
            isInitialized = true;

            if (debugMode) Debug.Log("[VRM10FaceController] 强制完整初始化完成");
        }

        public void SetParameter(string parameterName, float value)
        {
            var param = facialParameters.GetParameter(parameterName);
            if (param != null)
            {
                if (param.isExpression)
                {
                    SetExpression(param.name, value);
                }
                else
                {
                    // Logic for non-expression BlendShapes
                    param.currentValue = value;
                    OnParameterChanged?.Invoke(parameterName, value);
                }
            }
        }

        /// <summary>
        /// 设置BlendShape参数
        /// </summary>
        public void SetBlendShapeParameter(string parameterName, float value)
        {
            if (!isInitialized)
            {
                if (debugMode) Debug.LogWarning("[VRM10FaceController] 控制器未初始化，无法设置BlendShape参数");
                return;
            }

            if (showDetailedLogs) Debug.Log($"[VRM10FaceController] 设置BlendShape参数: {parameterName} = {value}");

            // 查找BlendShape参数
            var blendShapeParam = FindBlendShapeParameter(parameterName);
            if (blendShapeParam != null)
            {
                blendShapeParam.currentValue = value;

                // 立即应用到模型
                if (blendShapeParam.targetRenderer != null)
                {
                    blendShapeParam.targetRenderer.SetBlendShapeWeight(blendShapeParam.blendShapeIndex, value);
                }

                OnParameterChanged?.Invoke(parameterName, value);
            }
            else if (debugMode)
            {
                Debug.LogWarning($"[VRM10FaceController] 未找到BlendShape参数: {parameterName}");
            }
        }

        /// <summary>
        /// 查找BlendShape参数
        /// </summary>
        private BlendShapeParameter FindBlendShapeParameter(string parameterName)
        {
            if (facialParameters == null) return null;

            var allBlendShapeParams = new System.Collections.Generic.List<BlendShapeParameter>();
            allBlendShapeParams.AddRange(facialParameters.eyeParameters);
            allBlendShapeParams.AddRange(facialParameters.mouthParameters);
            allBlendShapeParams.AddRange(facialParameters.browParameters);
            allBlendShapeParams.AddRange(facialParameters.noseParameters);
            allBlendShapeParams.AddRange(facialParameters.cheekParameters);
            allBlendShapeParams.AddRange(facialParameters.faceShapeParameters);
            allBlendShapeParams.AddRange(facialParameters.expressionParameters);
            allBlendShapeParams.AddRange(facialParameters.otherParameters);

            return allBlendShapeParams.FirstOrDefault(p => p.name == parameterName);
        }
        
        public void SetExpressionFromWeb(string expressionData)
        {
            // Placeholder for WebGL integration
        }

        public void ResetFacialParameters()
        {
            facialParameters.ResetToDefault();
            UpdateAllParameters();
            OnParametersReset?.Invoke();
        }
        
        public void RandomizeFacialParameters()
        {
            // Placeholder
        }

        public void UpdateAllParameters()
        {
            if (!isInitialized) return;
            foreach (var param in facialParameters.GetAllParameters())
            {
                SetParameter(param.name, param.currentValue);
            }
        }

        public VRM10FacialParameters GetFacialParameters()
        {
            return facialParameters;
        }

        public FacialParametersData GetFacialParametersData()
        {
            var data = new FacialParametersData();
            foreach (var p in facialParameters.GetAllParameters())
            {
                if (p.isExpression)
                    data.standardExpressions[p.name] = p.currentValue;
                else
                    data.blendShapeParameters[p.name] = p.currentValue;
            }
            return data;
        }

        public void LoadFacialParametersData(FacialParametersData data)
        {
            if (data == null) return;
            ResetFacialParameters();
            foreach (var kvp in data.standardExpressions) SetParameter(kvp.Key, kvp.Value);
            foreach (var kvp in data.customExpressions) SetParameter(kvp.Key, kvp.Value);
            foreach (var kvp in data.blendShapeParameters) SetParameter(kvp.Key, kvp.Value);
            UpdateAllParameters();
        }
    }
} 